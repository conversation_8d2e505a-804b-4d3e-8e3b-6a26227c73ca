import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { PDFDocument, StandardFonts, rgb } from "pdf-lib"

export async function GET(request: NextRequest) {
  try {
    // Get guide type from query params
    const { searchParams } = new URL(request.url)
    const guideType = searchParams.get("type") || "complete"
    
    // Create a new PDF document
    const pdfDoc = await PDFDocument.create()
    
    // Add a new page to the document
    const page = pdfDoc.addPage([600, 800])
    
    // Get the standard font
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica)
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold)
    
    // Set initial cursor position
    let y = 750
    const margin = 50
    const lineHeight = 20

    // Add title
    page.drawText("Pet Care Guide", {
      x: margin,
      y,
      size: 24,
      font: boldFont,
      color: rgb(0, 0, 0),
    })
    
    y -= lineHeight * 2

    // Add content based on guide type
    const content = getGuideContent(guideType)
    
    for (const section of content) {
      // Draw section title
      page.drawText(section.title, {
        x: margin,
        y,
        size: 16,
        font: boldFont,
        color: rgb(0, 0, 0),
      })
      
      y -= lineHeight

      // Draw section content
      for (const line of section.content) {
        // Check if we need a new page
        if (y < margin) {
          const newPage = pdfDoc.addPage([600, 800])
          page = newPage
          y = 750
        }

        page.drawText(line, {
          x: margin + 10, // Indent content
          y,
          size: 12,
          font,
          color: rgb(0, 0, 0),
        })
        
        y -= lineHeight
      }
      
      y -= lineHeight // Add space between sections
    }

    // Save the PDF
    const pdfBytes = await pdfDoc.save()

    // Return the PDF as a download
    return new NextResponse(pdfBytes, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="pet-care-guide.pdf"`,
      },
    })

  } catch (error) {
    console.error("Error generating PDF:", error)
    return NextResponse.json(
      { error: "Failed to generate PDF" },
      { status: 500 }
    )
  }
}

function getGuideContent(type: string) {
  // This would ideally come from a database or CMS
  return [
    {
      title: "Getting Started",
      content: [
        "Essential supplies for new pet owners:",
        "- Food and water bowls",
        "- Species-appropriate food",
        "- Collar and ID tag",
        "- Leash (for dogs)",
        "- Appropriate housing/bedding",
        "- Grooming supplies",
      ]
    },
    {
      title: "Daily Care",
      content: [
        "Basic daily care routine:",
        "- Fresh water availability",
        "- Regular feeding schedule",
        "- Exercise and playtime",
        "- Bathroom breaks",
        "- Basic grooming",
      ]
    },
    {
      title: "Nutrition",
      content: [
        "Proper nutrition guidelines:",
        "- Age-appropriate food",
        "- Balanced diet",
        "- Portion control",
        "- Fresh water",
        "- Healthy treats",
      ]
    },
    {
      title: "Health & Wellness",
      content: [
        "Preventive care essentials:",
        "- Regular vet check-ups",
        "- Vaccinations",
        "- Dental care",
        "- Parasite prevention",
        "- Exercise routine",
      ]
    },
    {
      title: "Training & Behavior",
      content: [
        "Basic training principles:",
        "- Positive reinforcement",
        "- Consistency",
        "- Patience",
        "- Clear commands",
        "- Regular practice",
      ]
    }
  ]
}
