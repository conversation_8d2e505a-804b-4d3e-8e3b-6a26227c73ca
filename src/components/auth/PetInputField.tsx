"use client"

import { useState, forwardRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Eye, EyeOff, Mail, Lock, User, Phone } from "lucide-react"
import { cn } from "@/lib/utils"

interface PetInputFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string
  icon?: "mail" | "lock" | "user" | "phone"
  error?: string
  showPasswordToggle?: boolean
}

const iconMap = {
  mail: Mail,
  lock: Lock,
  user: User,
  phone: Phone,
}

// Animated paw prints that appear on focus
const FocusPaws = ({ show }: { show: boolean }) => (
  <AnimatePresence>
    {show && (
      <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-pink-300 text-xs"
            style={{
              right: `${10 + i * 15}px`,
              top: `${8 + i * 3}px`,
            }}
            initial={{ opacity: 0, scale: 0, rotate: -45 }}
            animate={{
              opacity: [0, 0.6, 0],
              scale: [0, 1, 0],
              rotate: [-45, 0, 45],
              x: [0, 10, 20],
            }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{
              duration: 1.5,
              delay: i * 0.2,
              repeat: Infinity,
              repeatDelay: 2,
            }}
          >
            🐾
          </motion.div>
        ))}
      </div>
    )}
  </AnimatePresence>
)

// Purring animation for the input container
const PurringContainer = ({ children, isPurring }: { children: React.ReactNode; isPurring: boolean }) => (
  <motion.div
    className="relative"
    animate={isPurring ? {
      scale: [1, 1.02, 1],
    } : {}}
    transition={{
      duration: 0.8,
      repeat: isPurring ? Infinity : 0,
      ease: "easeInOut",
    }}
  >
    {children}
  </motion.div>
)

// Wagging tail animation for icons
const WaggingIcon = ({ Icon, isWagging }: { Icon: React.ComponentType<any>; isWagging: boolean }) => (
  <motion.div
    animate={isWagging ? {
      rotate: [0, 10, -10, 0],
    } : {}}
    transition={{
      duration: 0.6,
      repeat: isWagging ? Infinity : 0,
      ease: "easeInOut",
    }}
  >
    <Icon className="h-5 w-5 text-gray-400 group-focus-within:text-pink-500 transition-colors duration-300" />
  </motion.div>
)

export const PetInputField = forwardRef<HTMLInputElement, PetInputFieldProps>(
  ({ label, icon, error, showPasswordToggle, className, type, ...props }, ref) => {
    const [isFocused, setIsFocused] = useState(false)
    const [showPassword, setShowPassword] = useState(false)
    const [value, setValue] = useState(props.value || "")
    
    const IconComponent = icon ? iconMap[icon] : null
    const inputType = showPasswordToggle && !showPassword ? "password" : type

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true)
      props.onFocus?.(e)
    }

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false)
      props.onBlur?.(e)
    }

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setValue(e.target.value)
      props.onChange?.(e)
    }

    return (
      <div className="space-y-2">
        {/* Label with floating animation */}
        <motion.label
          className="block text-sm font-medium text-gray-700"
          animate={isFocused ? { scale: 1.05, color: "#ec4899" } : {}}
          transition={{ duration: 0.2 }}
        >
          {label}
        </motion.label>

        {/* Input Container */}
        <PurringContainer isPurring={isFocused}>
          <div className="relative group">
            {/* Focus paw prints */}
            <FocusPaws show={isFocused} />
            
            {/* Icon */}
            {IconComponent && (
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
                <WaggingIcon Icon={IconComponent} isWagging={isFocused} />
              </div>
            )}

            {/* Input Field */}
            <motion.input
              ref={ref}
              type={inputType}
              className={cn(
                "w-full px-4 py-4 rounded-2xl border-2 border-gray-200 bg-white/50 backdrop-blur-sm",
                "focus:border-pink-400 focus:ring-4 focus:ring-pink-100 focus:bg-white/80",
                "transition-all duration-300 placeholder-gray-400",
                "hover:border-pink-300 hover:bg-white/60",
                IconComponent && "pl-12",
                showPasswordToggle && "pr-12",
                error && "border-red-300 focus:border-red-400 focus:ring-red-100",
                className
              )}
              value={value}
              onChange={handleChange}
              onFocus={handleFocus}
              onBlur={handleBlur}
              whileFocus={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
              {...props}
            />

            {/* Password Toggle */}
            {showPasswordToggle && (
              <motion.button
                type="button"
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-pink-500 transition-colors duration-300"
                onClick={() => setShowPassword(!showPassword)}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </motion.button>
            )}

            {/* Animated border glow */}
            <motion.div
              className="absolute inset-0 rounded-2xl pointer-events-none"
              animate={isFocused ? {
                boxShadow: [
                  "0 0 0 0 rgba(236, 72, 153, 0)",
                  "0 0 0 4px rgba(236, 72, 153, 0.1)",
                  "0 0 0 0 rgba(236, 72, 153, 0)",
                ],
              } : {}}
              transition={{
                duration: 2,
                repeat: isFocused ? Infinity : 0,
              }}
            />
          </div>
        </PurringContainer>

        {/* Error Message with bounce animation */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.9 }}
              transition={{ duration: 0.3 }}
              className="flex items-center space-x-2 text-red-600 text-sm"
            >
              <motion.span
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 0.5, repeat: 2 }}
              >
                ⚠️
              </motion.span>
              <span>{error}</span>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Success indicator */}
        {value && !error && (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            className="flex items-center space-x-2 text-green-600 text-sm"
          >
            <motion.span
              animate={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
            >
              ✅
            </motion.span>
            <span>Looking good!</span>
          </motion.div>
        )}
      </div>
    )
  }
)

PetInputField.displayName = "PetInputField"
