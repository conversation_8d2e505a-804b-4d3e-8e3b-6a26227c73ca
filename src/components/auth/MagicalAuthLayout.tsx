"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Heart, Sparkles, Star } from "lucide-react"
import Link from "next/link"

// Floating paw prints background
const FloatingPawPrints = () => {
  const [pawPositions, setPawPositions] = useState<Array<{
    id: number;
    left: number;
    top: number;
    size: number;
    duration: number;
    delay: number;
    opacity: number;
  }>>([])

  useEffect(() => {
    const positions = [...Array(15)].map((_, i) => ({
      id: i,
      left: Math.random() * 100,
      top: Math.random() * 100,
      size: 0.8 + Math.random() * 0.6,
      duration: 8 + Math.random() * 4,
      delay: Math.random() * 5,
      opacity: 0.1 + Math.random() * 0.2,
    }))
    setPawPositions(positions)
  }, [])

  if (pawPositions.length === 0) {
    return <div className="absolute inset-0 pointer-events-none" />
  }

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {pawPositions.map((paw) => (
        <motion.div
          key={paw.id}
          className="absolute text-pink-300"
          style={{
            left: `${paw.left}%`,
            top: `${paw.top}%`,
            fontSize: `${paw.size}rem`,
            opacity: paw.opacity,
          }}
          animate={{
            y: [0, -20, 0],
            rotate: [0, 10, -10, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: paw.duration,
            repeat: Infinity,
            delay: paw.delay,
            ease: "easeInOut",
          }}
        >
          🐾
        </motion.div>
      ))}
    </div>
  )
}

// Floating hearts animation
const FloatingHearts = () => {
  const [hearts, setHearts] = useState<Array<{
    id: number;
    left: number;
    delay: number;
  }>>([])

  useEffect(() => {
    const heartPositions = [...Array(8)].map((_, i) => ({
      id: i,
      left: Math.random() * 100,
      delay: Math.random() * 10,
    }))
    setHearts(heartPositions)
  }, [])

  if (hearts.length === 0) {
    return <div className="absolute inset-0 pointer-events-none" />
  }

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {hearts.map((heart) => (
        <motion.div
          key={heart.id}
          className="absolute text-pink-200"
          style={{
            left: `${heart.left}%`,
            bottom: "-20px",
          }}
          animate={{
            y: [0, -window.innerHeight - 100],
            opacity: [0, 0.6, 0.6, 0],
            scale: [0.5, 1, 1, 0.5],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            delay: heart.delay,
            ease: "linear",
          }}
        >
          💕
        </motion.div>
      ))}
    </div>
  )
}

// Sparkle effects
const SparkleEffect = ({ trigger }: { trigger: boolean }) => {
  return (
    <AnimatePresence>
      {trigger && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute text-yellow-400"
              style={{
                left: `${20 + i * 15}%`,
                top: `${30 + (i % 2) * 40}%`,
              }}
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
                rotate: [0, 180, 360],
              }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{
                duration: 1.5,
                delay: i * 0.1,
              }}
            >
              ✨
            </motion.div>
          ))}
        </div>
      )}
    </AnimatePresence>
  )
}

interface MagicalAuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle: string
}

export function MagicalAuthLayout({ children, title, subtitle }: MagicalAuthLayoutProps) {
  const [sparkle, setSparkle] = useState(false)

  useEffect(() => {
    const interval = setInterval(() => {
      setSparkle(true)
      setTimeout(() => setSparkle(false), 2000)
    }, 8000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-purple-50 via-blue-50 to-green-50" />
      
      {/* Animated Background Elements */}
      <FloatingPawPrints />
      <FloatingHearts />
      <SparkleEffect trigger={sparkle} />

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Header */}
        <motion.header
          className="p-6 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Link href="/" className="inline-flex items-center space-x-3 group">
            <motion.div
              className="relative"
              whileHover={{ scale: 1.1, rotate: 10 }}
              transition={{ duration: 0.3 }}
            >
              <Heart className="h-10 w-10 text-pink-500 group-hover:text-pink-600 transition-colors" />
              
              {/* Twinkling stars around logo */}
              {[...Array(4)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute text-yellow-400 text-xs"
                  style={{
                    top: `${[-10, -10, 50, 50][i]}px`,
                    left: `${[-10, 50, -10, 50][i]}px`,
                  }}
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.5,
                  }}
                >
                  ✨
                </motion.div>
              ))}
            </motion.div>
            
            <div className="text-left">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-pink-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
                PetAdopt
              </h1>
              <p className="text-sm font-script text-gray-600 italic">
                Forever Homes Begin Here ✨
              </p>
            </div>
          </Link>
        </motion.header>

        {/* Main Content Area */}
        <div className="flex-1 flex items-center justify-center p-6">
          <motion.div
            className="w-full max-w-md"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* Title Section */}
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                {title}
              </h2>
              <p className="text-gray-600">
                {subtitle}
              </p>
            </motion.div>

            {/* Form Container */}
            <motion.div
              className="bg-white/80 backdrop-blur-md rounded-3xl shadow-xl border border-white/20 p-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              {children}
            </motion.div>
          </motion.div>
        </div>

        {/* Footer */}
        <motion.footer
          className="p-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1 }}
        >
          <p className="text-xs text-gray-500">
            By continuing, you agree to our{" "}
            <Link href="/terms" className="text-pink-600 hover:text-pink-700 transition-colors">
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link href="/privacy" className="text-pink-600 hover:text-pink-700 transition-colors">
              Privacy Policy
            </Link>
          </p>
        </motion.footer>
      </div>
    </div>
  )
}
