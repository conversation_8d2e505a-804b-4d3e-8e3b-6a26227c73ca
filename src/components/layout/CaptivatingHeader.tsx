"use client"

import Link from "next/link"
import { useSession, signOut } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Heart, User, Menu, Search, Shield, Settings, ChevronDown, Sparkles, Star, Zap } from "lucide-react"
import { useState, useRef, useEffect } from "react"
import { UserRole } from "@prisma/client"
import { PermissionManager, Permission } from "@/lib/permissions"
import { SearchBar } from "./search-bar"
import { NavDropdown, adoptionDropdownItems, getInvolvedDropdownItems, resourcesDropdownItems, aboutDropdownItems } from "./nav-dropdown"
import { MobileMenu } from "./mobile-menu"
import { motion, AnimatePresence, useScroll, useTransform } from "framer-motion"
import { cn } from "@/lib/utils"
import { useNavigation } from "@/hooks/use-navigation"

// Paw print pattern component
const PawPattern = () => {
  const [pawPositions, setPawPositions] = useState<Array<{
    left: number;
    top: number;
    fontSize: number;
    duration: number;
    delay: number;
  }>>([])

  useEffect(() => {
    // Generate deterministic positions on client side only
    const positions = [...Array(20)].map((_, i) => ({
      left: Math.random() * 100,
      top: Math.random() * 100,
      fontSize: 12 + Math.random() * 8,
      duration: 3 + Math.random() * 2,
      delay: Math.random() * 2,
    }))
    setPawPositions(positions)
  }, [])

  if (pawPositions.length === 0) {
    // Return empty div during SSR to avoid hydration mismatch
    return <div className="absolute inset-0 opacity-10 overflow-hidden pointer-events-none" />
  }

  return (
    <div className="absolute inset-0 opacity-10 overflow-hidden pointer-events-none">
      {pawPositions.map((paw, i) => (
        <motion.div
          key={i}
          className="absolute text-orange-300"
          style={{
            left: `${paw.left}%`,
            top: `${paw.top}%`,
            fontSize: `${paw.fontSize}px`,
          }}
          animate={{
            y: [0, -10, 0],
            opacity: [0.3, 0.6, 0.3],
            rotate: [0, 5, -5, 0],
          }}
          transition={{
            duration: paw.duration,
            repeat: Infinity,
            delay: paw.delay,
          }}
        >
          🐾
        </motion.div>
      ))}
    </div>
  )
}

// Animated pet silhouette component
const AnimatedPetSilhouette = () => {
  const { scrollY } = useScroll()
  const x = useTransform(scrollY, [0, 1000], [0, 100])
  
  return (
    <motion.div
      className="absolute top-1/2 transform -translate-y-1/2 text-2xl opacity-60"
      style={{ x: `${x}%` }}
      animate={{
        y: [0, -5, 0],
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    >
      🐕
    </motion.div>
  )
}

// Twinkling logo component
const TwinklingLogo = () => {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className="relative"
    >
      <Link href="/" className="flex flex-col items-center group">
        <div className="flex items-center space-x-2 mb-1">
          <motion.div
            className="relative"
            whileHover={{ rotate: 10 }}
            transition={{ duration: 0.2 }}
          >
            <Heart className="h-8 w-8 text-pink-500 group-hover:text-pink-600 transition-colors" />
            
            {/* Twinkling stars around the heart */}
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute text-yellow-400"
                style={{
                  top: `${[-8, -8, 40, 40][i]}px`,
                  left: `${[-8, 40, -8, 40][i]}px`,
                }}
                animate={{
                  opacity: [0, 1, 0],
                  scale: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.5,
                }}
              >
                ✨
              </motion.div>
            ))}
          </motion.div>
          
          <div className="flex flex-col">
            <span className="text-xl font-bold bg-gradient-to-r from-pink-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
              PetAdopt
            </span>
            <motion.span
              className="text-xs font-script text-gray-600 italic"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              Forever Homes Begin Here ✨
            </motion.span>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}

// Enhanced search component
const EnhancedSearch = () => {
  const [isExpanded, setIsExpanded] = useState(false)
  
  return (
    <div className="hidden md:block">
      <motion.div
        className="relative"
        animate={{ width: isExpanded ? 320 : 48 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        {!isExpanded ? (
          <motion.button
            onClick={() => setIsExpanded(true)}
            className="w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-white"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Open search"
          >
            <Search className="h-5 w-5 text-gray-600" />
          </motion.button>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="relative"
          >
            <SearchBar
              className="w-full"
              placeholder="Search for your perfect companion... 🔍"
              onClose={() => setIsExpanded(false)}
            />
            <button
              onClick={() => setIsExpanded(false)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              aria-label="Close search"
            >
              ×
            </button>
          </motion.div>
        )}
      </motion.div>
    </div>
  )
}

// Glowing CTA button
const GlowingCTAButton = () => {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <Link href="/pets">
        <Button 
          size="lg"
          className="relative bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 hover:from-pink-600 hover:via-purple-600 hover:to-blue-600 text-white font-semibold px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
        >
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-pink-400 via-purple-400 to-blue-400 opacity-0 hover:opacity-20"
            animate={{
              opacity: [0, 0.3, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
            }}
          />
          <span className="relative z-10 flex items-center gap-2">
            <Heart className="w-5 h-5" />
            Meet Our Pets
            <Sparkles className="w-4 h-4" />
          </span>
        </Button>
      </Link>
    </motion.div>
  )
}

export function CaptivatingHeader() {
  const { data: session, status } = useSession()
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)
  const navigation = useNavigation()

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  return (
    <div>
      <motion.header
        className={cn(
          "fixed top-0 z-50 w-full transition-all duration-500",
          navigation.scrolled
            ? "bg-gradient-to-r from-pink-50/95 via-purple-50/95 to-blue-50/95 backdrop-blur-md shadow-lg"
            : "bg-gradient-to-r from-pink-50/90 via-purple-50/90 to-blue-50/90 backdrop-blur-sm"
        )}
        initial={{ y: -100 }}
        animate={{
          y: [0, -2, 0],
        }}
        transition={{
          duration: 0.6,
          ease: "easeOut",
          y: {
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }
        }}
      >
        {/* Paw pattern background */}
        <PawPattern />
        
        {/* Animated pet silhouette */}
        <AnimatedPetSilhouette />
        
        <div className="container mx-auto px-4">
          <div className="flex h-20 items-center justify-between">
            {/* Twinkling Logo */}
            <TwinklingLogo />

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1" role="navigation" aria-label="Main navigation">
              <NavDropdown
                title="Adoption"
                items={adoptionDropdownItems}
                className="px-3"
              />
              <NavDropdown
                title="Get Involved"
                items={getInvolvedDropdownItems}
                className="px-3"
              />
              <NavDropdown
                title="Resources"
                items={resourcesDropdownItems}
                className="px-3"
              />
              <NavDropdown
                title="About"
                items={aboutDropdownItems}
                className="px-3"
              />
            </nav>

            {/* Right side actions */}
            <div className="flex items-center space-x-4">
              {/* Enhanced Search */}
              <EnhancedSearch />
              
              {/* Glowing CTA Button */}
              <GlowingCTAButton />

              {/* User Authentication */}
              {status === "loading" ? (
                <motion.div
                  className="h-10 w-10 bg-gradient-to-r from-pink-200 to-purple-200 rounded-full"
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                />
              ) : session ? (
                <div className="flex items-center space-x-2">
                  {/* Favorites */}
                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                    <Link href="/favorites">
                      <Button variant="ghost" size="icon" className="relative hover:bg-white/50">
                        <Heart className="h-5 w-5 text-pink-600" />
                      </Button>
                    </Link>
                  </motion.div>

                  {/* User Menu */}
                  <div className="relative" ref={userMenuRef}>
                    <motion.button
                      onClick={() => setUserMenuOpen(!userMenuOpen)}
                      className="flex items-center space-x-2 p-2 rounded-full hover:bg-white/50 transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      aria-expanded={userMenuOpen}
                      aria-haspopup="true"
                    >
                      <div className="w-8 h-8 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-white" />
                      </div>
                      <ChevronDown className={cn(
                        "h-4 w-4 text-gray-600 transition-transform duration-200",
                        userMenuOpen && "rotate-180"
                      )} />
                    </motion.button>

                    <AnimatePresence>
                      {userMenuOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: -10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -10, scale: 0.95 }}
                          transition={{ duration: 0.15 }}
                          className="absolute right-0 mt-2 w-56 bg-white/95 backdrop-blur-md rounded-xl shadow-xl border border-white/20 py-2 z-50"
                          role="menu"
                        >
                          {/* User Info */}
                          <div className="px-4 py-3 border-b border-gray-100">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full flex items-center justify-center">
                                <User className="h-4 w-4 text-white" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 text-sm">
                                  {session.user?.name || session.user?.email}
                                </div>
                                <div className="text-xs text-gray-500 capitalize">
                                  {session.user?.role?.toLowerCase()}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Menu Items */}
                          <div className="py-1">
                            <Link href="/dashboard" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                              <User className="h-4 w-4 mr-3" />
                              Dashboard
                            </Link>
                            <Link href="/favorites" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                              <Heart className="h-4 w-4 mr-3" />
                              My Favorites
                            </Link>
                            {PermissionManager.hasPermission(session.user?.role as UserRole, Permission.ADMIN_ACCESS) && (
                              <Link href="/admin" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <Shield className="h-4 w-4 mr-3" />
                                Admin Panel
                              </Link>
                            )}
                            <Link href="/settings" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                              <Settings className="h-4 w-4 mr-3" />
                              Settings
                            </Link>
                          </div>

                          <div className="border-t border-gray-100 py-1">
                            <button
                              onClick={() => signOut()}
                              className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                            >
                              Sign Out
                            </button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link href="/auth/signin">
                      <Button variant="ghost" size="sm" className="hover:bg-white/50">Sign In</Button>
                    </Link>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link href="/auth/signup">
                      <Button size="sm" className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white">
                        Sign Up
                      </Button>
                    </Link>
                  </motion.div>
                </div>
              )}

              {/* Mobile Menu Button */}
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Button
                  variant="ghost"
                  size="icon"
                  className="lg:hidden hover:bg-white/50"
                  onClick={navigation.toggleMobileMenu}
                  aria-expanded={navigation.isMobileMenuOpen}
                  aria-label="Toggle mobile menu"
                >
                  <motion.div
                    animate={{ rotate: navigation.isMobileMenuOpen ? 90 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="h-5 w-5" />
                  </motion.div>
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={navigation.isMobileMenuOpen}
        onClose={navigation.closeMobileMenu}
      />
    </div>
  )
}
